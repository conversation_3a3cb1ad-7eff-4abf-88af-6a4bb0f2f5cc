import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import aiohttp

from services.core.error_service import handle_service_errors, retry_with_backoff
from services.core.http_client_service import get_http_client
from services.data.cache_service import get_cache
from utils.config import load_config

logger = logging.getLogger(__name__)

XAU_DATA_TTL = 30  # Sync with watchlist refresh interval
MT5_DATA_TTL = 60

class MT5DataService:
    """
    MT5/Exness data retrieval service using alternative data sources.
    Provides real-time and historical data for forex, gold, and other trading instruments.
    """
    
    def __init__(self):
        self.cache = get_cache("mt5_data", ttl=MT5_DATA_TTL)
        self.xau_cache = get_cache("xau_data", ttl=XAU_DATA_TTL)
        self.config = load_config()
        
        # Supported instruments mapping
        self.instrument_mapping = {
            'XAUUSD': {'yahoo': 'GC=F', 'name': 'Gold/USD', 'type': 'commodity'},
            'EURUSD': {'yahoo': 'EURUSD=X', 'name': 'Euro/USD', 'type': 'forex'},
            'GBPUSD': {'yahoo': 'GBPUSD=X', 'name': 'British Pound/USD', 'type': 'forex'},
            'USDJPY': {'yahoo': 'USDJPY=X', 'name': 'USD/Japanese Yen', 'type': 'forex'},
            'USDCHF': {'yahoo': 'USDCHF=X', 'name': 'USD/Swiss Franc', 'type': 'forex'},
            'AUDUSD': {'yahoo': 'AUDUSD=X', 'name': 'Australian Dollar/USD', 'type': 'forex'},
            'USDCAD': {'yahoo': 'USDCAD=X', 'name': 'USD/Canadian Dollar', 'type': 'forex'},
            'NZDUSD': {'yahoo': 'NZDUSD=X', 'name': 'New Zealand Dollar/USD', 'type': 'forex'},
            'EURGBP': {'yahoo': 'EURGBP=X', 'name': 'Euro/British Pound', 'type': 'forex'},
            'EURJPY': {'yahoo': 'EURJPY=X', 'name': 'Euro/Japanese Yen', 'type': 'forex'},
            'GBPJPY': {'yahoo': 'GBPJPY=X', 'name': 'British Pound/Japanese Yen', 'type': 'forex'},
            'XAGUSD': {'yahoo': 'SI=F', 'name': 'Silver/USD', 'type': 'commodity'},
            'WTIUSD': {'yahoo': 'CL=F', 'name': 'WTI Crude Oil/USD', 'type': 'commodity'},
            'SPX500': {'yahoo': '^GSPC', 'name': 'S&P 500 Index', 'type': 'index'},
            'US30': {'yahoo': '^DJI', 'name': 'Dow Jones Industrial Average', 'type': 'index'},
            'NAS100': {'yahoo': '^IXIC', 'name': 'NASDAQ Composite', 'type': 'index'}
        }
        
        logger.info("MT5 Data Service initialized")
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize trading symbol to internal format"""
        symbol = symbol.upper().replace('/', '').replace('_', '').replace('-', '')
        
        # Handle common variations
        if symbol in ['XAU', 'GOLD']:
            return 'XAUUSD'
        elif symbol in ['XAG', 'SILVER']:
            return 'XAGUSD'
        elif symbol in ['OIL', 'WTI', 'CRUDE']:
            return 'WTIUSD'
        elif symbol in ['SPX', 'SP500', 'SPY']:
            return 'SPX500'
        elif symbol in ['DOW', 'DJIA']:
            return 'US30'
        elif symbol in ['NASDAQ', 'NDX']:
            return 'NAS100'
        
        return symbol
    
    def get_yahoo_symbol(self, symbol: str) -> Optional[str]:
        """Get Yahoo Finance symbol for given trading symbol"""
        normalized = self.normalize_symbol(symbol)
        mapping = self.instrument_mapping.get(normalized)
        return mapping.get('yahoo') if mapping else None
    
    def get_instrument_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get instrument information"""
        normalized = self.normalize_symbol(symbol)
        mapping = self.instrument_mapping.get(normalized)
        if mapping:
            return {
                'symbol': normalized,
                'name': mapping['name'],
                'type': mapping['type'],
                'yahoo_symbol': mapping['yahoo']
            }
        return None
    
    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def get_real_time_price(self, symbol: str) -> Dict[str, Any]:
        """
        Get real-time price data for a trading instrument.
        
        Args:
            symbol: Trading symbol (e.g., XAUUSD, EURUSD)
            
        Returns:
            Dictionary containing price data
        """
        normalized_symbol = self.normalize_symbol(symbol)
        cache_key = f"realtime_price_{normalized_symbol}"

        # Use XAU-specific cache for gold data (faster refresh for watchlist)
        if normalized_symbol == 'XAUUSD':
            cached_data = self.xau_cache.get(cache_key)
        else:
            cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        yahoo_symbol = self.get_yahoo_symbol(normalized_symbol)
        if not yahoo_symbol:
            return {
                'success': False,
                'error': f'Unsupported symbol: {symbol}',
                'symbol': normalized_symbol
            }
        
        try:
            http_client = await get_http_client()
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}"
            params = {
                'interval': '1m',
                'range': '1d'
            }
            
            async with http_client.request('GET', url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        meta = result.get('meta', {})
                        
                        price_data = {
                            'success': True,
                            'symbol': normalized_symbol,
                            'name': self.instrument_mapping.get(normalized_symbol, {}).get('name', normalized_symbol),
                            'current_price': meta.get('regularMarketPrice'),
                            'currency': meta.get('currency', 'USD'),
                            'previous_close': meta.get('previousClose'),
                            'day_high': meta.get('regularMarketDayHigh'),
                            'day_low': meta.get('regularMarketDayLow'),
                            'volume': meta.get('regularMarketVolume'),
                            'market_state': meta.get('marketState'),
                            'timezone': meta.get('timezone'),
                            'timestamp': datetime.now().isoformat(),
                            'source': 'Yahoo Finance'
                        }
                        
                        # Calculate additional metrics
                        if price_data['current_price'] and price_data['previous_close']:
                            change = price_data['current_price'] - price_data['previous_close']
                            change_percent = (change / price_data['previous_close']) * 100
                            price_data['change'] = change
                            price_data['change_percent'] = change_percent
                        
                        # Use appropriate cache based on symbol
                        if normalized_symbol == 'XAUUSD':
                            self.xau_cache.set(cache_key, price_data, ttl=XAU_DATA_TTL)
                        else:
                            self.cache.set(cache_key, price_data, ttl=30)
                        logger.info(f"Retrieved real-time price for {normalized_symbol}: ${price_data['current_price']}")
                        return price_data
                    else:
                        logger.warning(f"No chart data found for {yahoo_symbol}")
                        return {
                            'success': False,
                            'error': 'No price data available',
                            'symbol': normalized_symbol
                        }
                else:
                    logger.error(f"Yahoo Finance API error: HTTP {response.status}")
                    return {
                        'success': False,
                        'error': f'API error: HTTP {response.status}',
                        'symbol': normalized_symbol
                    }
                    
        except Exception as e:
            logger.error(f"Error fetching real-time price for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': normalized_symbol
            }
    
    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def get_historical_data(self, symbol: str, interval: str = '1h', period: str = '5d') -> Dict[str, Any]:
        """
        Get historical OHLCV data for a trading instrument.
        
        Args:
            symbol: Trading symbol
            interval: Data interval (1m, 5m, 15m, 1h, 1d)
            period: Data period (1d, 5d, 1mo, 3mo, 6mo, 1y)
            
        Returns:
            Dictionary containing historical data
        """
        normalized_symbol = self.normalize_symbol(symbol)
        cache_key = f"historical_{normalized_symbol}_{interval}_{period}"
        
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        yahoo_symbol = self.get_yahoo_symbol(normalized_symbol)
        if not yahoo_symbol:
            return {
                'success': False,
                'error': f'Unsupported symbol: {symbol}',
                'symbol': normalized_symbol
            }
        
        try:
            http_client = await get_http_client()
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}"
            params = {
                'interval': interval,
                'range': period
            }
            
            async with http_client.request('GET', url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        timestamps = result.get('timestamp', [])
                        indicators = result.get('indicators', {})
                        quote = indicators.get('quote', [{}])[0] if indicators.get('quote') else {}
                        
                        # Process OHLCV data
                        ohlcv_data = []
                        opens = quote.get('open', [])
                        highs = quote.get('high', [])
                        lows = quote.get('low', [])
                        closes = quote.get('close', [])
                        volumes = quote.get('volume', [])
                        
                        for i in range(len(timestamps)):
                            if i < len(closes) and closes[i] is not None:
                                ohlcv_data.append({
                                    'timestamp': datetime.fromtimestamp(timestamps[i]).isoformat(),
                                    'open': opens[i] if i < len(opens) and opens[i] else None,
                                    'high': highs[i] if i < len(highs) and highs[i] else None,
                                    'low': lows[i] if i < len(lows) and lows[i] else None,
                                    'close': closes[i],
                                    'volume': volumes[i] if i < len(volumes) and volumes[i] else 0
                                })
                        
                        historical_data = {
                            'success': True,
                            'symbol': normalized_symbol,
                            'interval': interval,
                            'period': period,
                            'data_points': len(ohlcv_data),
                            'data': ohlcv_data,
                            'timestamp': datetime.now().isoformat(),
                            'source': 'Yahoo Finance'
                        }
                        
                        # Cache with longer TTL for historical data
                        cache_ttl = 300 if interval in ['1m', '5m'] else 900
                        self.cache.set(cache_key, historical_data, ttl=cache_ttl)
                        
                        logger.info(f"Retrieved {len(ohlcv_data)} historical data points for {normalized_symbol}")
                        return historical_data
                    else:
                        logger.warning(f"No historical data found for {yahoo_symbol}")
                        return {
                            'success': False,
                            'error': 'No historical data available',
                            'symbol': normalized_symbol
                        }
                else:
                    logger.error(f"Yahoo Finance API error: HTTP {response.status}")
                    return {
                        'success': False,
                        'error': f'API error: HTTP {response.status}',
                        'symbol': normalized_symbol
                    }
                    
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': normalized_symbol
            }
    
    async def get_multiple_prices(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get real-time prices for multiple symbols concurrently.
        
        Args:
            symbols: List of trading symbols
            
        Returns:
            Dictionary containing prices for all symbols
        """
        logger.info(f"Fetching prices for {len(symbols)} symbols")
        
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self.get_real_time_price(symbol))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        prices = {}
        successful = 0
        
        for i, result in enumerate(results):
            symbol = symbols[i]
            if isinstance(result, Exception):
                logger.error(f"Error fetching price for {symbol}: {result}")
                prices[symbol] = {
                    'success': False,
                    'error': str(result),
                    'symbol': symbol
                }
            else:
                prices[symbol] = result
                if result.get('success'):
                    successful += 1
        
        return {
            'success': True,
            'total_symbols': len(symbols),
            'successful_fetches': successful,
            'prices': prices,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_supported_instruments(self) -> List[Dict[str, Any]]:
        """Get list of supported trading instruments"""
        instruments = []
        for symbol, info in self.instrument_mapping.items():
            instruments.append({
                'symbol': symbol,
                'name': info['name'],
                'type': info['type'],
                'yahoo_symbol': info['yahoo']
            })
        return instruments

_mt5_service_instance: Optional[MT5DataService] = None

def get_mt5_service() -> MT5DataService:
    """Get singleton instance of MT5DataService"""
    global _mt5_service_instance
    if _mt5_service_instance is None:
        _mt5_service_instance = MT5DataService()
    return _mt5_service_instance

@handle_service_errors
async def get_mt5_price(symbol: str) -> Dict[str, Any]:
    """Get real-time price for MT5 symbol"""
    service = get_mt5_service()
    return await service.get_real_time_price(symbol)

@handle_service_errors
async def get_mt5_historical_data(symbol: str, interval: str = '1h', period: str = '5d') -> Dict[str, Any]:
    """Get historical data for MT5 symbol"""
    service = get_mt5_service()
    return await service.get_historical_data(symbol, interval, period)

@handle_service_errors
async def get_mt5_multiple_prices(symbols: List[str]) -> Dict[str, Any]:
    """Get multiple MT5 prices concurrently"""
    service = get_mt5_service()
    return await service.get_multiple_prices(symbols)
