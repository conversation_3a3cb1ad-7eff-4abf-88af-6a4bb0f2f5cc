import logging
import discord
from discord.ext import commands, tasks
from discord import app_commands
from typing import Optional
from datetime import datetime, timezone
import asyncio

from services.trading.trade_h_service import trade_h_data_service
from utils.config import load_config, get_trade_h_credentials

logger = logging.getLogger(__name__)

class TradeHCommands(commands.Cog):
    """Trade-H Dashboard Commands - Mirror of AdvancedTradingCommands"""

    def __init__(self, bot):
        self.bot = bot
        self.trade_h_channel_name = "trade-h"
        self.status_message: Optional[discord.Message] = None
        self.status_channel_id: Optional[int] = None

    async def cog_load(self):
        """Initialize Trade-H service and start status update task"""
        try:
            # Start data updates
            await trade_h_data_service.start_data_updates()

            self.update_status_message.start()
            logger.info("✅ Trade-H commands initialized with status updates")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Trade-H commands: {e}")

    async def setup_auto_status(self):
        """Setup automatic status message in trade-h channel"""
        try:
            # Find trade-h channel
            trade_h_channel = None
            for guild in self.bot.guilds:
                for channel in guild.text_channels:
                    if channel.name == self.trade_h_channel_name:
                        trade_h_channel = channel
                        break
                if trade_h_channel:
                    break

            if not trade_h_channel:
                logger.warning(f"⚠️ Trade-H channel '{self.trade_h_channel_name}' not found")
                return

            # Create status embed
            embed = await self._create_status_embed("default_user")

            # Send and pin the message
            message = await trade_h_channel.send(embed=embed)
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = trade_h_channel.id

            logger.info(f"✅ Auto status message created and pinned in #{trade_h_channel.name}")

        except Exception as e:
            logger.error(f"❌ Error setting up auto status: {e}")

    def cog_unload(self):
        """Stop status update task when cog unloads"""
        if self.update_status_message.is_running():
            self.update_status_message.cancel()

    def _check_trade_h_channel(self, interaction: discord.Interaction) -> bool:
        """Check if command is used in trade-h channel"""
        return interaction.channel.name == self.trade_h_channel_name

    @tasks.loop(seconds=60)
    async def update_status_message(self):
        """Update pinned status message every 60 seconds"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.status_message or not self.status_channel_id:
                    return

                channel = self.bot.get_channel(self.status_channel_id)
                if not channel:
                    logger.warning(f"Status channel {self.status_channel_id} not found")
                    return

                # Create status embed
                embed = await self._create_status_embed("default_user")

                # Update the pinned message
                await self.status_message.edit(embed=embed)
                return  # Success, exit the retry loop

            except discord.NotFound:
                logger.warning("Trade-H status message was deleted, recreating...")
                await self.setup_auto_status()
                return
            except Exception as e:
                retry_count += 1
                logger.error(f"❌ Error updating Trade-H status message (attempt {retry_count}): {e}")
                if retry_count < max_retries:
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                else:
                    logger.error(f"❌ Failed to update Trade-H status message after {max_retries} attempts")

    @update_status_message.before_loop
    async def before_update_status_message(self):
        """Wait for bot to be ready before starting status updates"""
        await self.bot.wait_until_ready()

        # Setup auto status message if not already exists
        if not self.status_message:
            await asyncio.sleep(5)  # Wait a bit for bot to fully initialize
            await self.setup_auto_status()



    async def _create_status_embed(self, user_id: str) -> discord.Embed:
        """Create comprehensive status embed - Mirror of AdvancedTradingCommands"""
        try:
            # Get data from data service
            summary_data = trade_h_data_service.get_summary_data()

            account_data = summary_data.get('account', {})
            positions_data = summary_data.get('positions', {})
            orders_data = summary_data.get('orders', {})

            # Calculate totals
            total_unrealized_pnl = positions_data.get('total_unrealized_pnl', 0)
            open_positions = positions_data.get('list', [])
            open_orders = orders_data.get('list', [])

            # Create embed
            embed = discord.Embed(
                title="📊 trade-h Trading Dashboard",
                color=0x00ff88 if total_unrealized_pnl >= 0 else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

            # Account Status - Simplified
            total_balance = account_data.get('total_balance', 0)
            free_balance = account_data.get('free_balance', 0)
            available_balance = free_balance

            embed.add_field(
                name="💰 Account Status",
                value=f"**Total Balance:** ${total_balance:,.2f}\n"
                      f"**Available Balance:** ${available_balance:,.2f}",
                inline=True
            )

            # P&L Summary - Count only non-TP/SL orders for pending orders
            non_tp_sl_orders_count = 0
            for order in open_orders:
                order_type = order.get('type', '').lower()
                if 'take_profit' not in order_type and 'stop' not in order_type:
                    non_tp_sl_orders_count += 1

            pnl_color = "🟢" if total_unrealized_pnl >= 0 else "🔴"
            embed.add_field(
                name="📈 P&L Summary",
                value=f"**Unrealized P&L:** {pnl_color} ${total_unrealized_pnl:,.2f}\n"
                      f"**Open Positions:** {len(open_positions)}\n"
                      f"**Pending Orders:** {non_tp_sl_orders_count}",
                inline=True
            )

            # Position Summary - Standardized format
            if open_positions:
                position_text = ""
                for i, pos in enumerate(open_positions[:5]):  # Show max 5
                    symbol = pos.get('symbol', 'Unknown')
                    # Clean symbol format - remove /USDT:USDT suffix
                    clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                    position_side = pos.get('position_side', 'Unknown').lower()  # Standardize to lowercase
                    entry_price = pos.get('entry_price', 0)
                    mark_price = pos.get('mark_price', 0)
                    pnl = pos.get('unrealized_pnl', 0)
                    size = pos.get('size', 0)

                    # Calculate position value
                    position_value = size * mark_price

                    pnl_emoji = "🟢" if pnl >= 0 else "🔴"

                    # Get TP/SL info and related orders
                    tp_price = pos.get('take_profit_price', 0)
                    sl_price = pos.get('stop_loss_price', 0)

                    position_text += f"**{clean_symbol}** {position_side.upper()} {pnl_emoji} ${pnl:,.2f}\n"
                    position_text += f"Entry: ${entry_price:,.4f} | Mark: ${mark_price:,.4f} | Value: ${position_value:,.2f} ({size:g})\n"

                    # Find TP/SL orders for this position to show values
                    tp_orders = []
                    sl_orders = []

                    for order in open_orders:
                        order_symbol = order.get('symbol', '')
                        order_type = order.get('type', '').lower()
                        order_position_side = order.get('position_side', '').upper()

                        # Match symbol and position side
                        if (order_symbol == symbol and order_position_side == position_side.upper()):
                            order_price = order.get('stop_price', 0) or order.get('trigger_price', 0) or order.get('price', 0)
                            order_value = size * order_price if order_price > 0 else 0

                            if 'take_profit' in order_type:
                                tp_orders.append({'price': order_price, 'value': order_value})
                            elif 'stop' in order_type and 'take_profit' not in order_type:
                                sl_orders.append({'price': order_price, 'value': order_value})

                    # Display TP orders
                    if tp_orders:
                        position_text += "TP:\n"
                        for tp_order in tp_orders:
                            position_text += f"${tp_order['price']:,.2f} | Value: ${tp_order['value']:,.2f}\n"
                    else:
                        position_text += "TP: None\n"

                    # Display SL orders
                    if sl_orders:
                        position_text += "SL:\n"
                        for sl_order in sl_orders:
                            position_text += f"${sl_order['price']:,.2f} | Value: ${sl_order['value']:,.2f}\n"
                    else:
                        position_text += "SL: None\n"

                    position_text += "\n"

                if len(open_positions) > 5:
                    position_text += f"... and {len(open_positions) - 5} more"

                embed.add_field(
                    name="📊 Open Positions",
                    value=position_text or "No open positions",
                    inline=False
                )

            # Orders Summary - Only show non-TP/SL orders (new position orders)
            if open_orders:
                # Filter out TP/SL orders - only show new position orders
                non_tp_sl_orders = []
                for order in open_orders:
                    order_type = order.get('type', '').lower()
                    # Skip TP/SL orders as they are now shown with positions
                    if 'take_profit' not in order_type and 'stop' not in order_type:
                        non_tp_sl_orders.append(order)

                if non_tp_sl_orders:
                    order_text = ""
                    for i, order in enumerate(non_tp_sl_orders[:5]):  # Show max 5
                        symbol = order.get('symbol', 'Unknown')
                        # Clean symbol format - remove /USDT:USDT suffix
                        clean_symbol = symbol.replace('/USDT:USDT', '').replace('USDT', '')

                        side = order.get('side', 'Unknown')
                        order_type = order.get('type', 'Unknown')
                        position_side = order.get('position_side', '').upper()

                        price = order.get('price', 0)
                        amount = order.get('amount', 0)
                        display_price = price

                        # Calculate order value
                        order_value = amount * display_price if amount > 0 and display_price > 0 else 0

                        # Format order type and side for display
                        if side.lower() == 'buy':
                            type_display = f"{order_type.upper()} (L)"
                        elif side.lower() == 'sell':
                            type_display = f"{order_type.upper()} (S)"
                        else:
                            type_display = order_type.upper()

                        order_text += f"**{clean_symbol}** {type_display} @ ${display_price:,.2f} | Value: ${order_value:,.2f}\n"

                    if len(non_tp_sl_orders) > 5:
                        order_text += f"... and {len(non_tp_sl_orders) - 5} more"

                    embed.add_field(
                        name="📋 Pending Orders",
                        value=order_text,
                        inline=False
                    )
                else:
                    # No non-TP/SL orders to show
                    embed.add_field(
                        name="📋 Pending Orders",
                        value="No new position orders",
                        inline=False
                    )

            embed.set_footer(text="Auto-updated every 60 seconds | Use /tradeh to pin this message")
            return embed

        except Exception as e:
            logger.error(f"❌ Error creating trade-h status embed: {e}")
            return discord.Embed(
                title="❌ trade-h Status Error",
                description=f"Error creating status: {str(e)}",
                color=0xff4444,
                timestamp=datetime.now(timezone.utc)
            )

    @app_commands.command(name="tradeh", description="Create/update pinned trade-h status message")
    async def create_status_message(self, interaction: discord.Interaction):
        """Create or update the pinned status message"""
        if not self._check_trade_h_channel(interaction):
            await interaction.response.send_message(
                f"❌ This command only works in #{self.trade_h_channel_name}",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            user_id = str(interaction.user.id)

            # Check if auto status already exists
            if self.status_message and self.status_channel_id == interaction.channel.id:
                # Update existing message
                embed = await self._create_status_embed(user_id)
                await self.status_message.edit(embed=embed)
                await interaction.followup.send("✅ Status message updated!", ephemeral=True)
                logger.info(f"✅ Trade-H status message updated by {interaction.user}")
                return

            # Create new status embed
            embed = await self._create_status_embed(user_id)

            # Send the message
            message = await interaction.followup.send(embed=embed)

            # Pin the message
            await message.pin()

            # Store message reference
            self.status_message = message
            self.status_channel_id = interaction.channel.id

            logger.info(f"✅ Trade-H status message created and pinned by {interaction.user}")

        except Exception as e:
            logger.error(f"❌ Error creating Trade-H status message: {e}")
            await interaction.followup.send(f"❌ Error creating status message: {str(e)}")

async def setup(bot):
    await bot.add_cog(TradeHCommands(bot))
