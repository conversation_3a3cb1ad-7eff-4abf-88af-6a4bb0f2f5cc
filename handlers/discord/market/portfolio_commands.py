"""
Discord slash commands for portfolio functionality
"""

import logging
import discord
from discord.ext import commands
from discord import app_commands

from services.core.portfolio_service import get_portfolio_service
from utils.ui_components import format_price_enhanced, format_percentage_enhanced
from utils.constants import EMOJI, DISCORD_FORMATTING, MESSAGE_TEMPLATES

logger = logging.getLogger(__name__)

class PortfolioCommands(commands.Cog):
    """Discord commands for cryptocurrency portfolio management"""

    def __init__(self, bot):
        self.bot = bot
        self.portfolio_service = get_portfolio_service(bot.db_service)

    @app_commands.command(name="portfolio", description="View your cryptocurrency portfolio summary")
    async def portfolio(self, interaction: discord.Interaction):
        """Display user's portfolio summary"""

        await interaction.response.defer(ephemeral=True)  # Private response

        try:
            user_id = str(interaction.user.id)
            portfolio_data = await self.portfolio_service.get_portfolio_summary(user_id)

            if not portfolio_data['success']:
                await interaction.followup.send(portfolio_data['message'], ephemeral=True)
                return

            # Create Discord embed
            embed = self.create_portfolio_embed(portfolio_data, interaction.user)

            # Send the embed
            await interaction.followup.send(embed=embed, ephemeral=True)

            logger.info(f"Portfolio command executed by {interaction.user} ({interaction.user.id})")

        except Exception as e:
            logger.error(f"Error in portfolio command: {e}")
            await interaction.followup.send("❌ An error occurred while fetching portfolio data.", ephemeral=True)

    @app_commands.command(name="holdings", description="View your cryptocurrency holdings")
    async def holdings(self, interaction: discord.Interaction):
        """Display user's holdings"""

        await interaction.response.defer(ephemeral=True)  # Private response

        try:
            user_id = str(interaction.user.id)
            holdings_data = await self.portfolio_service.get_holdings(user_id)

            if not holdings_data['success']:
                await interaction.followup.send(holdings_data['message'], ephemeral=True)
                return

            # Create Discord embed
            embed = self.create_holdings_embed(holdings_data, interaction.user)

            # Send the embed
            await interaction.followup.send(embed=embed, ephemeral=True)

            logger.info(f"Holdings command executed by {interaction.user} ({interaction.user.id})")

        except Exception as e:
            logger.error(f"Error in holdings command: {e}")
            await interaction.followup.send("❌ An error occurred while fetching holdings data.", ephemeral=True)



    @app_commands.command(name="add_trade", description="Thêm giao dịch vào danh mục đầu tư")
    @app_commands.describe(
        symbol="Symbol cryptocurrency (VD: BTC, ETH, BTCUSDT)",
        price="Giá mua trên 1 đơn vị (USD)",
        quantity="Số lượng coin (nhập 0 nếu muốn dùng tổng chi phí)",
        total_cost="Tổng chi phí (USD) - chỉ dùng khi quantity = 0"
    )
    async def add_trade(
        self,
        interaction: discord.Interaction,
        symbol: str,
        price: float,
        quantity: float,
        total_cost: float = 0.0
    ):
        """Add a trade to user's portfolio - supports quantity or total cost input"""

        await interaction.response.defer(ephemeral=True)

        try:
            user_id = str(interaction.user.id)

            # Input validation
            if not symbol or not symbol.strip():
                await interaction.followup.send("❌ Symbol không được để trống!", ephemeral=True)
                return

            if price <= 0:
                await interaction.followup.send("❌ Giá phải lớn hơn 0!", ephemeral=True)
                return

            # Smart symbol normalization
            from services.core.symbol_service import smart_normalize_symbol
            normalized_symbol = smart_normalize_symbol(symbol.strip().upper())

            # Simple logic: quantity = 0 means use total_cost
            is_total_cost = False
            final_quantity = quantity

            if quantity == 0:
                # Use total cost mode
                if total_cost <= 0:
                    await interaction.followup.send("❌ Khi quantity = 0, total_cost phải lớn hơn 0!", ephemeral=True)
                    return
                is_total_cost = True
                final_quantity = total_cost / price
                input_value = total_cost
            else:
                # Use quantity mode
                if quantity <= 0:
                    await interaction.followup.send("❌ Số lượng phải lớn hơn 0!", ephemeral=True)
                    return
                is_total_cost = False
                final_quantity = quantity
                input_value = quantity

            # Add the trade
            result = self.portfolio_service.add_trade(user_id, normalized_symbol, final_quantity, price)

            if result['success']:
                # Create enhanced success embed using templates
                title = MESSAGE_TEMPLATES['trade_command']['title_format'].format(
                    emoji=EMOJI['success'],
                    target_emoji=EMOJI['target']
                )

                embed = discord.Embed(
                    title=title,
                    color=DISCORD_FORMATTING['colors']['success'],
                    timestamp=discord.utils.utcnow()
                )

                total_value = final_quantity * price
                token = normalized_symbol.replace('USDT', '')

                # Use enhanced formatting functions
                qty_str = f"{final_quantity:.6f}" if final_quantity < 0.01 else f"{final_quantity:.4f}" if final_quantity < 1 else f"{final_quantity:.2f}"
                price_str = format_price_enhanced(price)
                total_str = format_price_enhanced(total_value)

                description = ""
                description += "```\n"
                description += "CHI TIẾT GIAO DỊCH\n"
                description += "═══════════════════════════\n"
                description += f"{EMOJI['coin']} Token:        {token}\n"
                description += f"{EMOJI['volume']} Số lượng:     {qty_str}\n"
                description += f"{EMOJI['price']} Giá mua:      {price_str}\n"
                description += f"{EMOJI['money']} Tổng giá trị: {total_str}\n"

                # Show input interpretation
                if is_total_cost:
                    description += f"{EMOJI['bulb']} Nhập chi phí: {format_price_enhanced(input_value)}\n"
                else:
                    description += f"{EMOJI['bulb']} Nhập SL:      {input_value}\n"

                description += "```\n"

                # Use template for success message
                success_msg = MESSAGE_TEMPLATES['trade_command']['success_message'].format(
                    sparkles=EMOJI['sparkles'],
                    bulb=EMOJI['bulb']
                )
                description += success_msg

                embed.description = description
                embed.set_footer(text=f"Thêm bởi {interaction.user.display_name} • {DISCORD_FORMATTING['footers']['portfolio']}")

                # Dynamic thumbnail based on symbol
                thumbnail_url = self._get_crypto_thumbnail(token)
                embed.set_thumbnail(url=thumbnail_url)

                await interaction.followup.send(embed=embed, ephemeral=True)

                # Log with input type
                input_type = "total_cost" if is_total_cost else "quantity"
                logger.info(f"Trade added ({input_type}): {interaction.user} ({user_id}) - {normalized_symbol} {quantity} @ {price}")

            else:
                # Enhanced error message using constants
                error_embed = discord.Embed(
                    title=f"{EMOJI['error']} KHÔNG THỂ THÊM GIAO DỊCH",
                    description=f"```\n{result['message']}\n```\n{EMOJI['bulb']} Kiểm tra lại thông tin và thử lại",
                    color=DISCORD_FORMATTING['colors']['error'],
                    timestamp=discord.utils.utcnow()
                )
                await interaction.followup.send(embed=error_embed, ephemeral=True)

        except ValueError as e:
            await interaction.followup.send(f"{EMOJI['error']} Lỗi định dạng số: {str(e)}", ephemeral=True)
        except Exception as e:
            logger.error(f"Error in add_trade command: {e}")
            error_embed = discord.Embed(
                title=f"{EMOJI['error']} LỖI HỆ THỐNG",
                description=f"```\nĐã xảy ra lỗi khi thêm giao dịch\n```\n{EMOJI['warning']} Vui lòng thử lại sau",
                color=DISCORD_FORMATTING['colors']['error'],
                timestamp=discord.utils.utcnow()
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)

    def _get_crypto_thumbnail(self, symbol: str) -> str:
        """Get appropriate thumbnail URL for crypto symbol"""
        symbol = symbol.upper()

        # Common crypto logos
        crypto_logos = {
            'BTC': 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
            'ETH': 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
            'BNB': 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
            'SOL': 'https://cryptologos.cc/logos/solana-sol-logo.png',
            'ADA': 'https://cryptologos.cc/logos/cardano-ada-logo.png',
            'DOT': 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
            'LINK': 'https://cryptologos.cc/logos/chainlink-link-logo.png',
            'MATIC': 'https://cryptologos.cc/logos/polygon-matic-logo.png',
            'AVAX': 'https://cryptologos.cc/logos/avalanche-avax-logo.png',
            'NEAR': 'https://cryptologos.cc/logos/near-protocol-near-logo.png',
            'ENA': 'https://cryptologos.cc/logos/bitcoin-btc-logo.png'  # Fallback for ENA
        }

        return crypto_logos.get(symbol, 'https://cryptologos.cc/logos/bitcoin-btc-logo.png')

    def create_portfolio_embed(self, portfolio_data, user) -> discord.Embed:
        """Create an enhanced Discord embed for portfolio data"""

        data = portfolio_data['data']

        if data['positions_count'] == 0:
            embed = discord.Embed(
                title="💼 CRYPTO PORTFOLIO 📊",
                description="```\n❌ Danh mục đầu tư trống\n\nSử dụng /add_trade để thêm giao dịch\n```",
                color=0x808080,  # Gray color
                timestamp=discord.utils.utcnow()
            )
        else:
            # Determine color based on PnL
            pnl = data['total_pnl']
            pnl_pct = data['total_pnl_percentage']
            color = 0x00ff88 if pnl >= 0 else 0xff4444  # Green for profit, red for loss

            embed = discord.Embed(
                title="💼 CRYPTO PORTFOLIO 📊",
                color=color,
                timestamp=discord.utils.utcnow()
            )

            # Portfolio summary with enhanced formatting
            pnl_emoji = "🟢" if pnl >= 0 else "🔴"
            pnl_sign = "+" if pnl >= 0 else "-"

            description = ""
            description += "```\n"
            description += "TỔNG QUAN DANH MỤC\n"
            description += "══════════════════════════════\n"
            description += f"💰 Tổng giá trị:    ${data['total_value']:>9,.2f}\n"
            description += f"💸 Tổng vốn đầu tư: ${data['total_cost']:>9,.2f}\n"
            description += f"{pnl_emoji} Lãi/Lỗ:         {pnl_sign}${abs(pnl):>8,.2f} ({pnl_sign}{abs(pnl_pct):.2f}%)\n"
            description += f"📋 Số vị thế:       {data['positions_count']:>9}\n"
            description += "```\n"

            embed.description = description

            # Add top positions with detailed info
            if data['positions']:
                positions_text = "```\n"
                positions_text += "TOKEN  SỐ LƯỢNG     GIÁ TB     GIÁ HT     P&L\n"
                positions_text += "─────────────────────────────────────────────\n"

                for position in data['positions'][:8]:  # Limit to 8 positions
                    symbol = position['symbol'].replace('USDT', '')
                    quantity = position['quantity']
                    avg_price = position['avg_price']
                    current_price = position['current_price']
                    pnl = position['pnl']
                    pnl_pct = position['pnl_percentage']

                    # Format symbol (max 5 chars for alignment)
                    symbol_display = symbol[:5]

                    # Format quantity with smart precision
                    if quantity >= 1000:
                        qty_str = f"{quantity:,.0f}"
                    elif quantity >= 1:
                        qty_str = f"{quantity:.2f}"
                    elif quantity >= 0.01:
                        qty_str = f"{quantity:.4f}"
                    else:
                        qty_str = f"{quantity:.6f}"

                    # Format prices with smart precision
                    if avg_price >= 1000:
                        avg_str = f"${avg_price:,.0f}"
                    elif avg_price >= 1:
                        avg_str = f"${avg_price:.2f}"
                    else:
                        avg_str = f"${avg_price:.4f}"

                    if current_price >= 1000:
                        cur_str = f"${current_price:,.0f}"
                    elif current_price >= 1:
                        cur_str = f"${current_price:.2f}"
                    else:
                        cur_str = f"${current_price:.4f}"

                    # Format P&L with emoji and sign
                    pnl_emoji = "🟢" if pnl_pct >= 0 else "🔴"
                    pnl_sign = "+" if pnl_pct >= 0 else "-"
                    pnl_str = f"{pnl_emoji}{pnl_sign}{abs(pnl_pct):.1f}%"

                    # Fixed-width formatting for perfect alignment
                    positions_text += f"{symbol_display:<5} {qty_str:>9} {avg_str:>9} {cur_str:>9} {pnl_str:>7}\n"

                if len(data['positions']) > 8:
                    positions_text += f"\n... và {len(data['positions']) - 8} vị thế khác"

                positions_text += "```"
                embed.add_field(name="📈 TOP POSITIONS", value=positions_text, inline=False)

        embed.set_footer(text=f"Danh mục của {user.display_name} • Dữ liệu từ Binance")
        embed.set_thumbnail(url="https://cryptologos.cc/logos/bitcoin-btc-logo.png")
        return embed

    def create_holdings_embed(self, holdings_data, user) -> discord.Embed:
        """Create an enhanced Discord embed for holdings data"""

        data = holdings_data['data']

        if not data:
            embed = discord.Embed(
                title="📋 CRYPTO HOLDINGS 💎",
                description="```\n❌ Không có tài sản nào\n\nSử dụng /add_trade để thêm giao dịch\n```",
                color=0x808080,  # Gray color
                timestamp=discord.utils.utcnow()
            )
        else:
            embed = discord.Embed(
                title="📋 CRYPTO HOLDINGS 💎",
                color=0x0099ff,  # Blue color
                timestamp=discord.utils.utcnow()
            )

            description = ""
            description += "```\n"
            description += "TOKEN  SỐ LƯỢNG     GIÁ MUA TB   GIÁ HIỆN TẠI  GIÁ TRỊ\n"
            description += "─────────────────────────────────────────────────────\n"

            total_value = 0

            for position in data[:12]:  # Limit to 12 holdings
                symbol = position['symbol'].replace('USDT', '')
                quantity = position['quantity']
                avg_price = position['avg_price']
                current_price = position['current_price']
                position_value = quantity * current_price
                total_value += position_value

                # Format symbol (max 5 chars)
                symbol_display = symbol[:5]

                # Format quantity with smart precision
                if quantity >= 1000:
                    qty_str = f"{quantity:,.0f}"
                elif quantity >= 1:
                    qty_str = f"{quantity:.2f}"
                elif quantity >= 0.01:
                    qty_str = f"{quantity:.4f}"
                else:
                    qty_str = f"{quantity:.6f}"

                # Format prices with smart precision
                if avg_price >= 1000:
                    avg_str = f"${avg_price:,.0f}"
                elif avg_price >= 1:
                    avg_str = f"${avg_price:.2f}"
                else:
                    avg_str = f"${avg_price:.4f}"

                if current_price >= 1000:
                    cur_str = f"${current_price:,.0f}"
                elif current_price >= 1:
                    cur_str = f"${current_price:.2f}"
                else:
                    cur_str = f"${current_price:.4f}"

                # Format value
                if position_value >= 1000:
                    val_str = f"${position_value:,.0f}"
                elif position_value >= 1:
                    val_str = f"${position_value:.2f}"
                else:
                    val_str = f"${position_value:.4f}"

                # Fixed-width formatting for perfect alignment
                description += f"{symbol_display:<5} {qty_str:>9} {avg_str:>10} {cur_str:>9} {val_str:>7}\n"

            if len(data) > 12:
                description += f"\n... và {len(data) - 12} tài sản khác"

            description += f"\n─────────────────────────────────────────────────────\n"
            description += f"💰 TỔNG GIÁ TRỊ:                         ${total_value:,.2f}\n"
            description += "```"

            embed.description = description

        embed.set_footer(text=f"Tài sản của {user.display_name} • Dữ liệu từ Binance")
        embed.set_thumbnail(url="https://cryptologos.cc/logos/ethereum-eth-logo.png")
        return embed

async def setup(bot):
    """Setup function called by Discord.py"""
    await bot.add_cog(PortfolioCommands(bot))
