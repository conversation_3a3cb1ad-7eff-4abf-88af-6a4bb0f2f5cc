VALID_TIMEFRAMES = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']

DEFAULT_TIMEFRAME = "1h"
DEFAULT_SYMBOL = "BTCUSDT"

MAX_SYMBOLS_PER_REQUEST = 10
MAX_CHART_PERIODS = 1000

BINANCE_API_BASE = "https://fapi.binance.com"
DEFAULT_CACHE_TTL = 300
PRICE_CACHE_TTL = 30

EMOJI = {
    'chart': '📈',
    'price': '💰',
    'up': '🟢',
    'down': '🔴',
    'warning': '⚠️',
    'error': '❌',
    'success': '✅',
    'loading': '⏳',
    'info': 'ℹ️',
    'fire': '🔥',
    'rocket': '🚀',
    'money': '💵',
    'coin': '🪙',
    'bull': '🐂',
    'bear': '🐻',
    'target': '🎯',
    'alarm': '🚨',
    'bell': '🔔',
    'star': '⭐',
    'diamond': '💎',
    'gem': '💍',
    'time': '🕐',
    'neutral': '⚪',
    'small_up': '📈',
    'small_down': '📉',
    'balance': '⚖️',
    'volume': '📊',
    'moon': '🌕',
    'trophy': '🏆',
    'sparkles': '✨',
    'bulb': '💡',
    'globe': '🌍',
    'newspaper': '📰',
    'briefcase': '💼',
    'lightning': '⚡',
    'magnifying_glass': '🔍',
    'portfolio': '📋',
    'trade': '💱',
    'profit': '💹',
    'loss': '📉',
    'calendar': '📅',
    'clock': '⏰',
    'high': '⬆️',
    'low': '⬇️',
    'change': '🔄',
    'green_circle': '🟢',
    'red_circle': '🔴',
    'yellow_circle': '🟡',
}

DISCORD_FORMATTING = {

    'colors': {
        'success': 0x00ff88,    # Green
        'error': 0xff4444,      # Red
        'warning': 0xffaa00,    # Yellow/Orange
        'info': 0x3498db,       # Blue
        'neutral': 0x95a5a6,    # Gray
        'profit': 0x00ff88,     # Green
        'loss': 0xff4444,       # Red
        'chart': 0x00ff88,      # Green for charts
    },

    'limits': {
        'embed_title': 256,
        'embed_description': 4096,
        'embed_field_name': 256,
        'embed_field_value': 1024,
        'embed_footer': 2048,
        'line_length': 50,      # Optimal for mobile
        'code_block_width': 40, # For code blocks in mobile
    },

    'footers': {
        'market_data': "Dữ liệu từ Binance & CoinGecko • ChartFix Bot",
        'chart_data': "Dữ liệu từ Binance Futures • ChartFix Bot",
        'portfolio': "ChartFix Portfolio Tracker",
        'alerts': "ChartFix Market Monitor • Cảnh báo tự động",
        'news': "ChartFix Market News • Cập nhật thị trường",
        'calendar': "ChartFix Economic Calendar • Lịch tài chính",
    }
}

PRICE_FORMAT_RULES = {
    'very_small': {'threshold': 0.0001, 'decimals': 8, 'prefix': '$'},
    'small': {'threshold': 0.1, 'decimals': 6, 'prefix': '$'},
    'medium': {'threshold': 1.0, 'decimals': 4, 'prefix': '$'},
    'large': {'threshold': 100.0, 'decimals': 2, 'prefix': '$'},
    'very_large': {'threshold': 1000.0, 'decimals': 0, 'prefix': '$', 'use_comma': True},
}

VOLUME_FORMAT_RULES = {
    'billion': {'threshold': 1_000_000_000, 'suffix': 'B', 'decimals': 1},
    'million': {'threshold': 1_000_000, 'suffix': 'M', 'decimals': 0},
    'thousand': {'threshold': 1_000, 'suffix': 'K', 'decimals': 0},
    'base': {'threshold': 0, 'suffix': '', 'decimals': 0},
}

# Percentage Calculation Standardization
PERCENTAGE_CALCULATION_CONFIG = {
    'default_method': 'rolling_24h',  # rolling_24h, daily_open, custom_period
    'default_source': 'binance_futures',  # binance_futures, binance_spot, coingecko
    'decimal_places': 2,
    'min_base_price': 0.00000001,
    'display_settings': {
        'include_sign': True,
        'include_emoji': True,
        'emoji_positive': '🟢',
        'emoji_negative': '🔴',
        'emoji_neutral': '⚪'
    }
}

# Data Source Priority for Percentage Calculations
PERCENTAGE_DATA_SOURCE_PRIORITY = [
    'binance_futures',  # Primary source - most accurate for trading
    'binance_spot',     # Secondary source - fallback
    'coingecko',        # Tertiary source - for comprehensive data
    'manual_calculation'  # Last resort - calculated from OHLCV
]

MESSAGE_TEMPLATES = {
    'price_command': {
        'title_format': "{emoji} {name} ({symbol}) Price",
        'description_format': "```\n{content}\n```",
        'field_separator': " | ",
        'line_separator': "\n",
    },

    'chart_command': {
        'title_format': "{emoji} {symbol} Chart",
        'description_format': "**Timeframe:** {timeframe}\n**Type:** {chart_type}\n**Indicators:** {indicators}",
    },

    'trade_command': {
        'title_format': "{emoji} GIAO DỊCH ĐÃ ĐƯỢC THÊM {target_emoji}",
        'success_message': "{sparkles} Giao dịch đã được lưu vào danh mục của bạn!\n{bulb} Sử dụng `/portfolio` để xem tổng quan",
    },



    'market_report': {
        'title': "{newspaper} DAILY MARKET REPORT {globe}",
        'section_separator': "══════════════════════════════",
    },


}
