import asyncio
import logging
import signal
import sys
import platform
import time
from typing import Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta, timezone

import discord
from discord.ext import commands, tasks

from logging_config import setup_logging, important_logger
from utils.config import get_discord_token, get_admin_id, get_guild_id
from services.data.database_service import DatabaseService
from services.market.market_service import get_market_service
from services.core.portfolio_service import get_portfolio_service

logger = logging.getLogger(__name__)

class BotStats:
    def __init__(self):
        self.start_time = datetime.now(timezone.utc)
        self.command_count = defaultdict(int)
        self.error_count = defaultdict(int)
        self.response_times = defaultdict(deque)
        self.api_calls = defaultdict(int)

    def record_command(self, command_name: str, response_time: float):
        self.command_count[command_name] += 1
        self.response_times[command_name].append(response_time)

        if len(self.response_times[command_name]) > 100:
            self.response_times[command_name].popleft()

    def record_error(self, error_type: str):
        self.error_count[error_type] += 1

    def record_api_call(self, api_name: str):
        self.api_calls[api_name] += 1

    def get_uptime(self) -> timedelta:
        return datetime.now(timezone.utc) - self.start_time

    def get_avg_response_time(self, command_name: str) -> float:
        times = self.response_times.get(command_name, [])
        return sum(times) / len(times) if times else 0.0

class CryptoBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True  # Enabled in Developer Portal

        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )

        self.db_service = DatabaseService()
        self.market_service = get_market_service()
        self.portfolio_service = get_portfolio_service(self.db_service)

        from handlers.discord.alerts.market_monitor_alerts import get_market_monitor_alert_handler
        self.market_monitor_handler = get_market_monitor_alert_handler(self)

        from services.market.market_monitor_service import get_market_monitor_service
        self.market_monitor = get_market_monitor_service()



        from services.market.volume_alert_service import get_volume_alert_service
        self.volume_alert_service = get_volume_alert_service()

        from handlers.discord.alerts.volume_alert_handler import get_volume_alert_handler
        self.volume_alert_handler = get_volume_alert_handler(self)

        from services.discord.discord_bot_logging import get_discord_bot_logging_service
        self.discord_logger = get_discord_bot_logging_service(self)



        # Initialize price alert services
        from services.market.price_alert_service import get_price_alert_service
        self.price_alert_service = get_price_alert_service()

        from handlers.discord.alerts.price_alert_handler import get_price_alert_handler
        self.price_alert_handler = get_price_alert_handler(self)

        # Initialize short-term price alert service
        from services.market.short_term_price_alert_service import get_short_term_alert_service
        self.short_term_alert_service = get_short_term_alert_service()



        # Register price alert callbacks
        self.price_alert_service.register_alert_callback(self.price_alert_handler.handle_price_alert)
        self.short_term_alert_service.add_alert_callback(self.price_alert_handler.handle_price_alert)



        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

        self.is_ready = False
        self.stats = BotStats()

        self.active_watchlists = {}

        self.cooldowns = defaultdict(dict)



        self.consecutive_errors = 0
        self.max_consecutive_errors = 5

        self.last_health_check = datetime.now()
        self.health_check_interval = timedelta(minutes=5)

    def check_cooldown(self, user_id: int, command_name: str, cooldown_seconds: int = 5) -> bool:
        now = datetime.now()
        user_cooldowns = self.cooldowns[user_id]

        if command_name in user_cooldowns:
            time_since_last = (now - user_cooldowns[command_name]).total_seconds()
            if time_since_last < cooldown_seconds:
                return False

        user_cooldowns[command_name] = now
        return True

    def get_cooldown_remaining(self, user_id: int, command_name: str, cooldown_seconds: int = 5) -> float:
        now = datetime.now()
        user_cooldowns = self.cooldowns[user_id]

        if command_name in user_cooldowns:
            time_since_last = (now - user_cooldowns[command_name]).total_seconds()
            remaining = cooldown_seconds - time_since_last
            return max(0, remaining)
        return 0

    async def record_command_execution(self, command_name: str, start_time: float, success: bool = True):
        response_time = time.time() - start_time
        self.stats.record_command(command_name, response_time)

        if success:
            self.consecutive_errors = 0
        else:
            self.consecutive_errors += 1
            self.stats.record_error(command_name)

        if response_time > 5.0:
            logger.warning(f"Slow command execution: {command_name} took {response_time:.2f}s")

    async def health_check(self):
        now = datetime.now()
        if (now - self.last_health_check) >= self.health_check_interval:
            self.last_health_check = now

    async def setup_hook(self):
        try:
            await self.load_extension('handlers.discord.market.watchlist_commands')
            await self.load_extension('handlers.discord.market.portfolio_commands')
            await self.load_extension('handlers.discord.market.market_commands')
            await self.load_extension('handlers.discord.admin.admin_commands')

            await self.load_extension('handlers.discord.trading.advanced_commands')
            await self.load_extension('handlers.discord.trading.position_commands')
            await self.load_extension('handlers.discord.trading.trade_h_commands')
            await self.load_extension('handlers.discord.market.price_alert_commands')
            await self.load_extension('handlers.discord.alerts.volume_commands')
        except Exception as e:
            logger.error(f"Error loading extensions: {e}")
            self.stats.record_error("extension_load")

    async def on_ready(self):
        if not self.is_ready:
            self.is_ready = True
            important_logger.info(f"Enhanced Discord bot logged in as {self.user} (ID: {self.user.id})")
            important_logger.info(f"Bot is in {len(self.guilds)} guilds")

            try:
                if self.guild_id and any(guild.id == int(self.guild_id) for guild in self.guilds):
                    guild = discord.Object(id=int(self.guild_id))
                    self.tree.copy_global_to(guild=guild)
                    await self.tree.sync(guild=guild)
                elif len(self.guilds) > 0:
                    guild = self.guilds[0]
                    self.tree.copy_global_to(guild=guild)
                    await self.tree.sync(guild=guild)
                else:
                    logger.warning("Bot not in any guilds, cannot sync commands")
                    important_logger.warning("⚠️ Bot not invited to any Discord servers!")
                    important_logger.info("📋 Use this link to invite bot:")
                    important_logger.info(f"https://discord.com/api/oauth2/authorize?client_id={self.user.id}&permissions=94224&scope=bot%20applications.commands")
            except Exception as e:
                logger.error(f"Error syncing commands: {e}")
                self.stats.record_error("command_sync")

            if not self.watchlist_updater.is_running():
                self.watchlist_updater.start()

            if not self.health_monitor.is_running():
                self.health_monitor.start()



            self.market_monitor.register_alert_callback(self.market_monitor_handler.send_market_alert)

            self.volume_alert_service.register_alert_callback(self.volume_alert_handler.handle_volume_alert)

            await self.market_monitor.start_monitoring()
            await self.volume_alert_service.start_monitoring()

            # Start short-term price alert monitoring
            try:
                asyncio.create_task(self.short_term_alert_service.start_monitoring())
                important_logger.info("✅ Short-term price alert service started successfully")
            except Exception as e:
                logger.error(f"Error starting short-term price alert service: {e}")
                important_logger.error(f"❌ Failed to start short-term price alert service: {e}")

            important_logger.info("🚀 Enhanced ChartFix Bot is ready and operational!")
            important_logger.info(f"🤖 Discord bot active")
            important_logger.info(f"📊 Available commands: /watchlist, /portfolio, /holdings, /add_trade, /market, /rates, /c, /p, /stats, /pin, /unpin, /pins")
            important_logger.info(f"📊 Dashboard features: Trading Status Dashboard (#trade), Trade-H Dashboard (#trade-h)")

            try:
                await self.discord_logger.send_status_notification(
                    "ready",
                    "🚀 **ChartFix Bot đã sẵn sàng hoạt động!**",
                    f"📊 Kết nối thành công đến {len(self.guilds)} server\n"
                    f"⚡ Các lệnh có sẵn: /watchlist, /portfolio, /market, /rates, /calendar, /c, /p, /price"
                )
            except Exception as e:
                logger.error(f"Failed to send ready notification: {e}")

    async def on_command_error(self, ctx, error):
        if isinstance(error, commands.CommandNotFound):
            return

        if isinstance(error, commands.CommandOnCooldown):
            await ctx.send(f"⏰ Command on cooldown. Try again in {error.retry_after:.1f} seconds.", ephemeral=True)
            return

        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You don't have permission to use this command.", ephemeral=True)
            return

        logger.error(f"Command error in {ctx.command}: {type(error).__name__}: {error}")
        self.stats.record_error(f"command_{type(error).__name__}")

        error_msg = "❌ An error occurred while processing your command."

        if "timeout" in str(error).lower():
            error_msg = "⏱️ Command timed out. Please try again."
        elif "forbidden" in str(error).lower():
            error_msg = "🚫 Bot doesn't have permission to perform this action."
        elif "not found" in str(error).lower():
            error_msg = "🔍 Requested resource not found."

        if ctx.interaction:
            if not ctx.interaction.response.is_done():
                await ctx.interaction.response.send_message(error_msg, ephemeral=True)
            else:
                await ctx.interaction.followup.send(error_msg, ephemeral=True)
        else:
            await ctx.send(error_msg)

    async def on_application_command_error(self, interaction: discord.Interaction, error):
        logger.error(f"Slash command error: {type(error).__name__}: {error}")
        self.stats.record_error(f"slash_{type(error).__name__}")

        error_msg = "❌ An error occurred while processing your command."

        if "timeout" in str(error).lower():
            error_msg = "⏱️ Command timed out. Please try again."
        elif "forbidden" in str(error).lower():
            error_msg = "🚫 Bot doesn't have permission to perform this action."
        elif "rate limit" in str(error).lower():
            error_msg = "🐌 Rate limited. Please wait a moment and try again."

        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(error_msg, ephemeral=True)
            else:
                await interaction.followup.send(error_msg, ephemeral=True)
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

    async def on_error(self, event, *_args, **_kwargs):
        logger.exception(f"Bot error in event {event}")
        self.stats.record_error(f"event_{event}")

        try:
            await self.discord_logger.send_error_notification(
                f"Event Error: {event}",
                f"Lỗi xảy ra trong event {event}",
                event,
                "ERROR"
            )
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")

        if self.consecutive_errors >= self.max_consecutive_errors:
            logger.critical(f"Too many consecutive errors ({self.consecutive_errors}), attempting recovery")
            important_logger.warning("🚨 Bot entering recovery mode due to excessive errors")

            try:
                await self.discord_logger.send_error_notification(
                    "Critical System Error",
                    f"Bot gặp quá nhiều lỗi liên tiếp ({self.consecutive_errors}). Đang khởi động lại hệ thống...",
                    "error_recovery",
                    "CRITICAL"
                )
            except Exception as e:
                logger.error(f"Failed to send critical error notification: {e}")

            self.consecutive_errors = 0
            await asyncio.sleep(5)

    def is_admin(self, user_id: int) -> bool:
        return str(user_id) == self.admin_id

    @tasks.loop(seconds=90)
    async def watchlist_updater(self):
        try:
            await self.health_check()

            if self.active_watchlists:
                watchlist_data = await self.market_service.get_watchlist_data()

                if watchlist_data['success']:
                    # Get market data for watchlist symbols
                    symbols = list(watchlist_data['prices'].keys())
                    market_data = {}
                    
                    # Get daily open prices for all symbols first
                    daily_opens = await self.market_service.get_daily_open_prices(symbols)

                    for symbol in symbols:
                        formatted_symbol = f"{symbol.replace('USDT', '')}/USDT:USDT"

                        # Get ticker data and calculate daily open change
                        try:
                            from services.market.market_service import get_binance_futures_exchange
                            from services.market.percentage_calculation_service import get_percentage_service

                            exchange = get_binance_futures_exchange()
                            ticker = exchange.fetch_ticker(formatted_symbol)

                            # Use unified percentage calculation service with daily open method
                            percentage_service = get_percentage_service()

                            # Prepare daily candle data with today's open price
                            daily_candle_data = {
                                'daily_open': daily_opens.get(symbol, 0.0)
                            }

                            # Use daily open percentage calculation
                            percentage_result = percentage_service.extract_binance_daily_open_percentage(
                                ticker, daily_candle_data
                            )

                            daily_change = percentage_result.value if percentage_result.is_valid else 0.0
                            volume_24h = ticker.get('quoteVolume', 0.0) or 0.0

                            market_data[symbol] = {
                                'daily_change': daily_change,
                                'volume_24h': volume_24h,
                                'percentage_source': percentage_result.source.value,
                                'percentage_method': percentage_result.method.value,
                                'daily_open_price': daily_opens.get(symbol, 0.0)
                            }

                            logger.debug(f"Daily Open Change for {symbol}: {daily_change:.2f}% (Open: {daily_opens.get(symbol, 0.0)}, Current: {ticker.get('last', 0.0)})")

                        except Exception as e:
                            logger.error(f"Error fetching ticker for {symbol}: {e}")
                            market_data[symbol] = {
                                'daily_change': 0.0,
                                'volume_24h': 0.0,
                                'daily_open_price': 0.0
                            }
                    
                    # Check for price alerts
                    await self.price_alert_service.check_price_alerts(
                        watchlist_data['prices'], 
                        market_data
                    )
                    
                    # Update active watchlists
                    for channel_id, watchlist_info in list(self.active_watchlists.items()):
                        try:
                            # Handle both old format (just message_id) and new format (dict with message_id and change_type)
                            if isinstance(watchlist_info, dict):
                                message_id = watchlist_info['message_id']
                                change_type = watchlist_info.get('change_type', '24h')
                            else:
                                # Backward compatibility: old format was just message_id
                                message_id = watchlist_info
                                change_type = '24h'

                            channel = self.get_channel(channel_id)
                            if channel:
                                message = await channel.fetch_message(message_id)

                                # Get watchlist commands cog to use the proper embed creation
                                watchlist_cog = self.get_cog('WatchlistCommands')
                                if watchlist_cog:
                                    embed = await watchlist_cog.create_enhanced_watchlist_embed(watchlist_data, {}, change_type)
                                    await message.edit(embed=embed)
                                else:
                                    # Fallback to old method if cog not found
                                    from utils.ui_components import format_watchlist_message
                                    embed_content = format_watchlist_message(watchlist_data)

                                    embed = discord.Embed(
                                        title="🚀 CRYPTO WATCHLIST 📈",
                                        description=embed_content,
                                        color=0x00ff88,
                                        timestamp=discord.utils.utcnow()
                                    )
                                    embed.set_footer(text="Dữ liệu từ Binance Futures • Tự động cập nhật mỗi 90s")
                                    await message.edit(embed=embed)
                            else:
                                del self.active_watchlists[channel_id]

                        except discord.NotFound:
                            del self.active_watchlists[channel_id]
                        except Exception as e:
                            logger.error(f"Error updating watchlist in channel {channel_id}: {e}")

                self.stats.record_api_call("watchlist_update")

        except Exception as e:
            logger.error(f"Error in watchlist updater: {e}")
            self.stats.record_error("watchlist_updater")

    @tasks.loop(minutes=5)
    async def health_monitor(self):
        try:
            await self.health_check()

            now = datetime.now()
            cutoff = now - timedelta(hours=1)

            for user_id in list(self.cooldowns.keys()):
                user_cooldowns = self.cooldowns[user_id]
                for command in list(user_cooldowns.keys()):
                    if user_cooldowns[command] < cutoff:
                        del user_cooldowns[command]

                if not user_cooldowns:
                    del self.cooldowns[user_id]

        except Exception as e:
            logger.error(f"Error in health monitor: {e}")
            self.stats.record_error("health_monitor")

    @watchlist_updater.before_loop
    async def before_watchlist_updater(self):
        await self.wait_until_ready()

    @health_monitor.before_loop
    async def before_health_monitor(self):
        await self.wait_until_ready()


bot: Optional[CryptoBot] = None

async def main():
    global bot

    try:
        token = get_discord_token()

        if not token:
            logger.error("DISCORD_BOT_TOKEN not found in config.yaml")
            important_logger.error("Error: DISCORD_BOT_TOKEN not configured in config.yaml")
            sys.exit(1)

        setup_logging()
        important_logger.info("Starting Discord bot...")

        bot = CryptoBot()

        def signal_handler(sig_name):
            important_logger.info(f"Received {sig_name}, shutting down...")
            asyncio.create_task(shutdown())

        if platform.system() != 'Windows':
            loop = asyncio.get_event_loop()
            for sig in [signal.SIGINT, signal.SIGTERM, signal.SIGHUP]:
                loop.add_signal_handler(sig, lambda s=sig: signal_handler(s.name))

        await bot.start(token)

    except KeyboardInterrupt:
        important_logger.info("Bot interrupted by user")
    except Exception as e:
        logger.exception(f"Error starting bot: {e}")
        important_logger.error(f"Error starting bot: {e}")
        sys.exit(1)

async def shutdown():
    global bot

    if not bot:
        return

    important_logger.info("🛑 Shutting down bot...")

    try:
        await bot.discord_logger.send_status_notification(
            "shutdown",
            "🛑 **ChartFix Bot đang tắt...**",
            "Hệ thống sẽ ngừng hoạt động trong giây lát"
        )
    except Exception as e:
        logger.error(f"Failed to send shutdown notification: {e}")

    # Cancel all running tasks
    bot.watchlist_updater.cancel()



    try:
        if hasattr(bot, 'volume_alert_service'):
            await bot.volume_alert_service.stop_monitoring()
        important_logger.info("✅ Volume alert service stopped")
    except Exception as e:
        logger.error(f"Error stopping volume alert service: {e}")

    try:
        from services.core.http_client_service import close_http_client
        await close_http_client()
        important_logger.info("✅ HTTP client service closed")
    except Exception as e:
        logger.error(f"Error closing HTTP client service: {e}")

    try:
        await bot.close()
        # Give extra time for Discord connections to close properly
        await asyncio.sleep(0.5)
        important_logger.info("Discord bot shutdown complete")
    except Exception as e:
        logger.error(f"Error during bot shutdown: {e}")
        important_logger.info("Discord bot shutdown completed with errors")


if __name__ == "__main__":
    try:
        if platform.system() == 'Windows':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        asyncio.run(main())

    except KeyboardInterrupt:
        print("Bot stopped by user")
        # Ensure proper cleanup on Ctrl+C
        try:
            asyncio.run(shutdown())
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
    except Exception as e:
        logger.exception("Unhandled exception:")
        # Ensure cleanup even on unexpected errors
        try:
            asyncio.run(shutdown())
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")
        sys.exit(1)
